[build]
  command = "npm run build"
  publish = ".next"

[build.environment]
  NODE_VERSION = "18"

# Handle client-side routing - this is CRITICAL for SPA routing
[[redirects]]
  from = "/*"
  to = "/"
  status = 200
  force = false

# Environment variables that need to be set in Netlify UI
# NEXT_PUBLIC_SUPABASE_URL
# NEXT_PUBLIC_SUPABASE_ANON_KEY
# SUPABASE_SERVICE_ROLE_KEY
# TWILIO_ACCOUNT_SID
# TWILIO_AUTH_TOKEN
# TWILIO_WHATSAPP_NUMBER
# OPENROUTER_API_KEY
# INTASEND_PUBLISHABLE_KEY
# INTASEND_SECRET_KEY

# Environment variables that need to be set in Netlify UI
# NEXT_PUBLIC_SUPABASE_URL
# NEXT_PUBLIC_SUPABASE_ANON_KEY
# SUPABASE_SERVICE_ROLE_KEY
# TWILIO_ACCOUNT_SID
# TWILIO_AUTH_TOKEN
# TWILIO_WHATSAPP_NUMBER
