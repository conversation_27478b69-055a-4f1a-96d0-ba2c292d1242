'use client'

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'
import { LoadingState } from '@/components/LoadingState'
import Pagination, { usePagination } from '@/components/Pagination'


interface AIInsight {
  id: string
  insight_type: 'trend_analysis' | 'risk_detection' | 'recommendation' | 'department_insight' | 'employee_insight'
  title: string
  description: string
  severity: 'info' | 'warning' | 'critical'
  department?: string
  employee_id?: string
  data_points: any
  action_items: string[]
  is_read: boolean
  is_dismissed: boolean
  created_at: string
}

export default function InsightsPage() {
  // ALL HOOKS MUST BE DECLARED FIRST - NO CONDITIONAL RETURNS BEFORE THIS
  const { profile } = useAuth()
  const [insights, setInsights] = useState<AIInsight[]>([])
  const [selectedType, setSelectedType] = useState('all')
  const [selectedSeverity, setSelectedSeverity] = useState('all')
  const [showDismissed, setShowDismissed] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [generatingInsights, setGeneratingInsights] = useState(false)
  const [autoGenerated, setAutoGenerated] = useState(false)
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20
  })

  // Pagination hook
  const {
    currentPage,
    itemsPerPage,
    handlePageChange,
    handleItemsPerPageChange,
    resetPagination
  } = usePagination(20)

  // Generate new insights via API route - MUST BE DEFINED BEFORE useEffect
  const generateNewInsights = async (auto = false) => {
    try {
      setGeneratingInsights(true)
      setError(null)
      if (typeof window !== 'undefined') {
        console.log('[Insights] Generating new insights for org:', profile?.organization?.id)
      }
      const response = await fetch('/api/ai/insights', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ organizationId: profile?.organization?.id })
      })
      const result = await response.json()
      if (typeof window !== 'undefined') {
        console.log('[Insights] Generate insights API response:', result)
      }
      if (result.success) {
        // Refetch insights
        const res = await fetch(`/api/ai/insights?organizationId=${profile?.organization?.id}&limit=50`)
        const result2 = await res.json()
        if (typeof window !== 'undefined') {
          console.log('[Insights] Refetched insights after generation:', result2)
        }
        const processed = (result2.insights || []).map((insight: any) => ({
          ...insight,
          action_items: typeof insight.action_items === 'string' ? JSON.parse(insight.action_items) : insight.action_items || []
        }))
        setInsights(processed)
        // Show backend warning if present (from either result2 or result)
        if (result2.warning) {
          setError(result2.warning)
        } else if (result.warning) {
          setError(result.warning)
        }
      } else {
        // Show backend error or warning
        if (result.error) setError(result.error)
        else if (result.warning) setError(result.warning)
        else setError('Failed to generate insights')
      }
    } catch (err: any) {
      setError(err.message || 'Failed to generate insights')
      if (typeof window !== 'undefined') {
        console.error('[Insights] Error generating insights:', err)
      }
    } finally {
      setGeneratingInsights(false)
    }
  }

  // Load insights function - MUST BE DEFINED BEFORE useEffect
  const loadInsights = async () => {
    if (!profile?.organization?.id) return
    setLoading(true)
    setError(null)
    try {
      const res = await fetch(`/api/ai/insights?organizationId=${profile.organization.id}&page=${currentPage}&limit=${itemsPerPage}`)
      const result = await res.json()
      if (result.success) {
        const processed = (result.insights || []).map((insight: any) => ({
          ...insight,
          action_items: typeof insight.action_items === 'string' ? JSON.parse(insight.action_items) : insight.action_items || []
        }))
        setInsights(processed)

        // Update pagination state
        if (result.pagination) {
          setPagination(result.pagination)
        }

        if (result.warning) {
          setError(result.warning)
        }
      } else {
        setError(result.error || 'Failed to load insights')
      }
    } catch (err: any) {
      setError(err.message || 'Failed to load insights')
    } finally {
      setLoading(false)
    }
  }

  // ALL useEffect HOOKS MUST BE DECLARED HERE
  // Reset pagination when filters change
  useEffect(() => {
    resetPagination()
  }, [selectedType, selectedSeverity, showDismissed, resetPagination])

  // Load insights data when profile, pagination, or filters change
  useEffect(() => {
    if (profile?.organization?.id) {
      loadInsights()
    }
  }, [profile?.organization?.id, currentPage, itemsPerPage])

  // Fetch insights via API route
  useEffect(() => {
    const fetchInsights = async (tryAutoGenerate = true) => {
      if (!profile?.organization?.id) {
        setLoading(false)
        setError('No organization found in your profile. Please contact support.')
        if (typeof window !== 'undefined') {
          console.error('[Insights] No organization ID in profile:', profile)
        }
        return
      }
      try {
        setLoading(true)
        setError(null)
        if (typeof window !== 'undefined') {
          console.log('[Insights] Fetching insights for org:', profile.organization.id)
        }
        const res = await fetch(`/api/ai/insights?organizationId=${profile.organization.id}&limit=50`)
        const result = await res.json()
        if (typeof window !== 'undefined') {
          console.log('[Insights] Insights API response:', result)
        }
        if (!result.success) throw new Error(result.error || 'Failed to fetch insights')
        const processed = (result.insights || []).map((insight: any) => ({
          ...insight,
          action_items: typeof insight.action_items === 'string' ? JSON.parse(insight.action_items) : insight.action_items || []
        }))
        setInsights(processed)
        // Show backend warning if present
        if (result.warning) {
          setError(result.warning)
        }
        // If no insights and not already auto-generated, try to generate
        if (processed.length === 0 && tryAutoGenerate && !autoGenerated && !result.warning) {
          setAutoGenerated(true)
          if (typeof window !== 'undefined') {
            console.log('[Insights] No insights found, auto-generating...')
          }
          await generateNewInsights(true)
        }
      } catch (err: any) {
        setError(err.message)
        if (typeof window !== 'undefined') {
          console.error('[Insights] Error fetching insights:', err)
        }
      } finally {
        setLoading(false)
      }
    }
    // Only fetch if org ID is present
    if (profile?.organization?.id) {
      if (typeof window !== 'undefined') {
        console.log('[Insights] useEffect triggered for org:', profile.organization.id)
      }
      fetchInsights()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [profile?.organization?.id])



  // Authentication is handled by dashboard layout AuthGuard
  if (!profile?.organization_id) {
    return <LoadingState message="Loading organization data..." />
  }


  // Use API route to mark as read
  const markAsRead = async (insightId: string) => {
    try {
      const res = await fetch('/api/ai/insights', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ insightId, isRead: true })
      })
      const result = await res.json()
      if (!result.success) throw new Error(result.error || 'Failed to mark as read')
      setInsights(prev => prev.map(insight => 
        insight.id === insightId ? { ...insight, is_read: true } : insight
      ))
    } catch (err: any) {
      console.error('Error marking insight as read:', err)
    }
  }

  // Use API route to dismiss
  const dismissInsight = async (insightId: string) => {
    try {
      const res = await fetch('/api/ai/insights', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ insightId, isDismissed: true })
      })
      const result = await res.json()
      if (!result.success) throw new Error(result.error || 'Failed to dismiss insight')
      setInsights(prev => prev.map(insight => 
        insight.id === insightId ? { ...insight, is_dismissed: true } : insight
      ))
    } catch (err: any) {
      console.error('Error dismissing insight:', err)
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200'
      case 'warning': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default: return 'bg-blue-100 text-blue-800 border-blue-200'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'risk_detection':
        return (
          <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        )
      case 'trend_analysis':
        return (
          <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        )
      case 'recommendation':
        return (
          <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
        )
      default:
        return (
          <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        )
    }
  }

  // Profile and organization are guaranteed to exist here due to auth guards above

  if (loading) {
    return <LoadingState message="Loading insights..." />
  }

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Header */}
      <header className="bg-white border-b border-gray-100">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">AI Insights</h1>
              <p className="text-gray-600 text-sm mt-1">
                AI-powered insights and recommendations for your team
              </p>
            </div>
            
            <div className="flex items-center space-x-3">
              <button 
                onClick={() => generateNewInsights(false)}
                disabled={generatingInsights}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium flex items-center space-x-2 disabled:opacity-50"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span>{generatingInsights ? 'Generating...' : 'Generate New Insights'}</span>
              </button>
              <button 
                onClick={() => generateNewInsights(false)}
                className="border border-gray-300 text-gray-700 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors"
                title="Refresh"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Content */}
      <main className="p-4">
        {/* Show error only in insights section */}
        {error && (
          <div className="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
            {error}
          </div>
        )}

        {/* AI Insights & Recommendations Card (moved from analytics) */}
        {insights.length > 0 && (
          <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100 mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">AI Insights & Recommendations</h2>
            <div className="space-y-4">
              {insights.slice(0, 3).map((insight: any, i: number) => {
                const colorMap: Record<string, string> = {
                  info: 'bg-blue-50 border-blue-200 text-blue-900',
                  warning: 'bg-yellow-50 border-yellow-200 text-yellow-900',
                  critical: 'bg-red-50 border-red-200 text-red-900'
                }
                const sev = typeof insight.severity === 'string' ? insight.severity : 'info'
                const colorClass = colorMap[sev] || 'bg-gray-50 border-gray-200 text-gray-900'
                return (
                  <div key={i} className={`p-4 rounded-xl border ${colorClass}`}>
                    <div className="flex items-start space-x-3">
                      <div className={`w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0 ${colorClass.split(' ')[0]}`}></div>
                      <div>
                        <h3 className={`font-semibold ${colorClass.split(' ')[2]}`}>{insight.title}</h3>
                        <p className={`${colorClass.split(' ')[2]} text-sm mt-1`}>{insight.description}</p>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        )}

        {/* Filters */}
        <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100 mb-6">
          <div className="flex flex-col md:flex-row gap-4 items-center">
            <div className="flex gap-4 flex-1">
              <select 
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="text-gray-900 border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Types</option>
                <option value="risk_detection">Risk Detection</option>
                <option value="trend_analysis">Trend Analysis</option>
                <option value="recommendation">Recommendations</option>
                <option value="department_insight">Department Insights</option>
                <option value="employee_insight">Employee Insights</option>
              </select>
              
              <select 
                value={selectedSeverity}
                onChange={(e) => setSelectedSeverity(e.target.value)}
                className="text-gray-900 border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Severities</option>
                <option value="critical">Critical</option>
                <option value="warning">Warning</option>
                <option value="info">Info</option>
              </select>
            </div>
            
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={showDismissed}
                onChange={(e) => setShowDismissed(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">Show dismissed</span>
            </label>
          </div>
        </div>

        {/* Insights List */}
        <div className="space-y-4">
          {insights.length === 0 ? (
            <div className="bg-white p-12 rounded-2xl shadow-sm border border-gray-100 text-center">
              <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No insights found</h3>
              <p className="text-gray-600 mb-4">
                {error
                  ? error
                  : insights.length === 0 
                    ? "To generate insights, add employees and collect check-ins in your organization."
                    : "Try adjusting your filters or generate new insights"
                }
              </p>
              {insights.length === 0 && !error && (
                <button 
                  onClick={() => generateNewInsights(false)}
                  disabled={generatingInsights}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  {generatingInsights ? 'Generating...' : 'Generate Insights'}
                </button>
              )}
            </div>
          ) : (
            insights.map((insight) => (
              <InsightCard 
                key={insight.id} 
                insight={insight} 
                onMarkAsRead={markAsRead}
                onDismiss={dismissInsight}
              />
            ))
          )}
        </div>

        {/* Pagination */}
        {!loading && !error && insights.length > 0 && (
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100">
            <Pagination
              currentPage={pagination.currentPage}
              totalPages={pagination.totalPages}
              totalItems={pagination.totalItems}
              itemsPerPage={pagination.itemsPerPage}
              onPageChange={handlePageChange}
              onItemsPerPageChange={handleItemsPerPageChange}
              loading={loading}
            />
          </div>
        )}
      </main>
    </div>
  )
}

// Individual Insight Card Component
function InsightCard({ 
  insight, 
  onMarkAsRead, 
  onDismiss 
}: { 
  insight: AIInsight
  onMarkAsRead: (id: string) => void
  onDismiss: (id: string) => void
}) {
  // Icon for each insight type
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'risk_detection':
        return (
          <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        )
      case 'trend_analysis':
        return (
          <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        )
      case 'positive_trend':
        return (
          <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        )
      default:
        return (
          <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" fill="none" />
          </svg>
        )
    }
  };
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'from-red-500/40 to-red-600/50 border-red-200/40 text-red-900';
      case 'warning':
        return 'from-yellow-400/40 to-yellow-500/50 border-yellow-200/40 text-yellow-900';
      case 'info':
      default:
        return 'from-blue-500/40 to-blue-600/50 border-blue-200/40 text-blue-900';
    }
  };
  return (
    <div className={`glass backdrop-blur-xl bg-gradient-to-br ${getSeverityColor(insight.severity)} border rounded-2xl shadow-2xl p-6 mb-4 transition-all duration-200 hover:scale-[1.02]`}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          {getTypeIcon(insight.insight_type)}
          <h3 className="font-bold text-lg">{insight.title}</h3>
        </div>
        <div className="flex gap-2">
          {!insight.is_read && (
            <button onClick={() => onMarkAsRead(insight.id)} className="px-3 py-1 bg-blue-600 text-white rounded-lg font-semibold shadow hover:bg-blue-700 transition-all">Mark as Read</button>
          )}
          <button onClick={() => onDismiss(insight.id)} className="px-3 py-1 bg-gray-200 text-gray-700 rounded-lg font-semibold shadow hover:bg-gray-300 transition-all">Dismiss</button>
        </div>
      </div>
      <p className="text-gray-800 mb-2">{insight.description}</p>
      <div className="flex items-center space-x-3">
        <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getSeverityColor(insight.severity)}`}>
          {insight.severity.charAt(0).toUpperCase() + insight.severity.slice(1)}
        </span>
        
        {insight.department && (
          <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-700 rounded-full">
            {insight.department}
          </span>
        )}
        
        <span className="text-xs text-gray-500">
          {new Date(insight.created_at).toLocaleDateString()}
        </span>
      </div>
    </div>
  );
}
