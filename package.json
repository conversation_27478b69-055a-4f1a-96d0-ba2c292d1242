{"name": "staff-pulse", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next build && next export", "netlify-build": "npm run build"}, "dependencies": {"@headlessui/react": "^2.2.4", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.50.2", "clsx": "^2.1.1", "critters": "^0.0.23", "date-fns": "^4.1.0", "dotenv": "^17.0.1", "intasend-inlinejs-sdk": "^4.0.7", "jszip": "^3.10.1", "lucide-react": "^0.525.0", "next": "15.3.4", "react": "^19.0.0", "react-datepicker": "^8.4.0", "react-dom": "^19.0.0", "recharts": "^3.0.2", "tailwind-merge": "^3.3.1", "twilio": "^5.3.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "png-to-ico": "^2.1.8", "sharp": "^0.34.2", "tailwindcss": "^4", "typescript": "^5"}}